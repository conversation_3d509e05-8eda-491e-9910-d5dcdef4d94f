<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Office extends Model
{
    protected $primaryKey = 'id';
    protected $table = 'offices';
    protected $fillable = [
        'country_name_tr', 'country_name_en', 'country_name_de',
        'office_name_tr', 'office_name_en', 'office_name_de',
        'phone_code', 'phone', 'fax_code', 'fax', 'gsm_code', 'gsm', 'email',
        'address_tr', 'address_en', 'address_de',
        'is_active', 'sort_order'
    ];

    public $timestamps = true;
} 