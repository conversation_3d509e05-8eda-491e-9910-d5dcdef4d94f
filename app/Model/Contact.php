<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    protected $primaryKey='id';
    protected $table='contact';
    protected $fillable = [
        'center_phone', 'center_fax', 'center_email', 'center_address', 'center_address_en', 'center_address_de',
        'iz_phone', 'iz_fax', 'iz_email', 'iz_address', 'iz_address_en', 'iz_address_de',
        'management_phone', 'management_fax', 'management_email', 'management_address', 'management_address_en', 'management_address_de',
        'company', 'company_en', 'company_de'
    ];

    public $timestamps = false;
}