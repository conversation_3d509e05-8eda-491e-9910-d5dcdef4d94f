<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\Contact;
use App\Model\OngoingProjectCatalog;
use App\Model\Service;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ServiceController extends Controller
{
    public function index($url_slug){

        $urlSlug = explode('-', $url_slug);
        $id = end($urlSlug);
        
        $service = Service::where('id', $id)->first();
      
        $services = Service::where('del', 0)->orderBy('row', 'asc')->get();

        $contact = Contact::first();

        $catalog = OngoingProjectCatalog::first();

        $banner = Banner::where('id',5)->first();

        return view('layouts.site.service.index', compact('service', 'contact', 'services', 'catalog', 'banner'));
    }
}
