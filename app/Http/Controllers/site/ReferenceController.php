<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\OngoingProjectCatalog;
use App\Model\Reference;
use App\Model\ReferenceCategory;
use App\Model\ReferenceGallery;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ReferenceController extends Controller
{
    public function index($url_slug){

        $urlSlug = explode('-', $url_slug);
        $id = end($urlSlug);

        $references = Reference::where('reference_category_id',$id)->where('del', 0)->orderBy('row', 'asc')->get();

        $cat = ReferenceCategory::where('id', $id)->select('id', 'reference_category_tr', 'reference_category_en', 'reference_category_de')->first();

        $referenceCategories = ReferenceCategory::where('del', 0)->orderBy('row', 'asc')->get();


        $banner = Banner::where('id',6)->first();

        $catalog = OngoingProjectCatalog::first();

        return view('layouts.site.references', compact('references', 'banner', 'catalog', 'cat', 'referenceCategories'));
    }

    public function detail($url_slug){

        $urlSlug = explode('-', $url_slug);
        $id = end($urlSlug);

        $reference = Reference::select('reference.id','reference.reference_category_id', 'reference.image_url', 'reference.reference_tr','reference.reference_en', 'reference.reference_de',
            'reference_category.reference_category_tr', 'reference_category.reference_category_en', 'reference_category.reference_category_de', 'reference.location', 'reference.location_en', 'reference.location_de', 'reference.employer', 'reference.employer_en', 'reference.employer_de', 'reference.note',
            'reference.note_en', 'reference.note_de')
            ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
            ->where('reference_category.del', 0)
            ->where('reference.del', 0)
            ->where('reference.id', $id)
            ->first();

        $galleries = ReferenceGallery::where('reference_id', $id)->where('del', 0)->get();

        $referenceCategories = ReferenceCategory::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',6)->first();

        $catalog = OngoingProjectCatalog::first();

        return view('layouts.site.reference_detail', compact('reference', 'galleries', 'banner', 'referenceCategories', 'catalog'));

    }

    public function generalIndex(){

        $categories = ReferenceCategory::where('del', 0)->orderBy('row', 'asc')->get();

        $references = Reference::select('reference.id','reference.reference_category_id', 'reference.image_url', 'reference.reference_tr','reference.reference_en', 'reference.reference_de',
             'reference_category.reference_category_tr', 'reference_category.reference_category_en', 'reference_category.reference_category_de')
            ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
            ->where('reference_category.del', 0)
            ->where('reference.del', 0)
            ->get();

        $banner = Banner::where('id',6)->first();

        return view('layouts.site.reference_general', compact('categories', 'references', 'banner'));
    }
}