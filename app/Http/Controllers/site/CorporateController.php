<?php

namespace App\Http\Controllers\site;

use App\Model\About;
use App\Model\ActivityDocument;
use App\Model\Banner;
use App\Model\Management;
use App\Model\HealtyAndSafety;
use App\Model\QualityDocument;
use App\Model\ReferenceLogo;
use App\Model\HrContent;
use App\Model\Hr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;


class CorporateController extends Controller
{
    public function about(){

        $about = About::first();

        $referenceLogos = ReferenceLogo::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',1)->first();

        return view('layouts.site.corporate.about', compact('about', 'referenceLogos', 'banner'));
    }

 public function healty(){

        $healty = HealtyAndSafety::first();

        $banner = Banner::where('id',12)->first();

        return view('layouts.site.corporate.healty_safety', compact('healty','banner'));
    }

    public function management(){

        $banner = Banner::where('id',2)->first();

        $managements = Management::where('del', 0)->orderBy('row', 'asc')->get();

        return view('layouts.site.corporate.management', compact('managements', 'banner'));
    }

    public function activityDocument(){

        $activityDocuments = ActivityDocument::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',3)->first();

        return view('layouts.site.corporate.activitydocument', compact('activityDocuments', 'banner'));

    }

    public function qualityDocument(){

        $qualityDocuments = QualityDocument::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',4)->first();

        return view('layouts.site.corporate.qualitydocument', compact('qualityDocuments', 'banner'));

    }
    
    public function hr(){

        $banner = Banner::where('id', 11)->first();

        $hr = HrContent::first();

        return view('layouts.site.corporate.hr', compact( 'banner', 'hr'));

    }

    public function hrCreate(Request $request){

        if($request->hasFile('pdf')){

            $file = $request->file('pdf');

            $extension = $file->getClientOriginalExtension();
            $fileName = sha1(uniqid()) . '.' . $extension;

            if($extension === 'pdf'){

                $file->move(base_path() . '/upload/hr/', $fileName);

$url =  '/upload/hr/'.$fileName;



                 Hr::where('id', $request->get('catalog_id'))->insert([

                    'pdf' => $fileName,
                    'date' => Carbon::now()

                ]);

$array['url'] = $url ;


Mail::send('mail.ik', ['info' => $array], function ($message)
                {

                    $message->from('<EMAIL>', 'LATEK');
                    $message->subject('Latek İK Formu');
                    $message->to('<EMAIL> ') ;  

                });

                return response()->json("1");


            }else{

                return response()->json("0");
            }

        }else{

            return response()->json("2");

        }

    }
    
    
}