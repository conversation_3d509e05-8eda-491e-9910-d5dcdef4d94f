<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\News;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class NewsController extends Controller
{
    public function index(){

        $news = News::where('del', 0)->orderBy('date', 'desc')->paginate(6);

        $eng = array("January","February","March","April","May","June","July","August","September","October","November","December");

        $tur = array("Ocak","Şuba<PERSON>","Mart","Nisan","May<PERSON><PERSON>","Haziran","Temmuz","Ağustos","Eylül","E<PERSON>","Kasım","Aralık");

        $banner = Banner::where('id',10)->first();


        return view('layouts.site.news', compact('news', 'eng', 'tur', 'banner'));
    }

    public function detail($url_slug){

        $urlSlug = explode('-', $url_slug);
        $id = end($urlSlug);

        $news = News::where('id', $id)->first();

        $eng = array("January","February","March","April","May","June","July","August","September","October","November","December");

        $tur = array("Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık");

        $banner = Banner::where('id',10)->first();

        $recent_news = News::where('del', 0)->orderBy('date', 'desc')->limit(8)->get();

        return view('layouts.site.news_detail', compact('news', 'eng', 'tur', 'recent_news', 'banner'));
    }
}
