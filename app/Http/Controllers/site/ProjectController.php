<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\Contact;
use App\Model\GoingProject;
use App\Model\GoingProjectGallery;
use App\Model\OngoingProjectCatalog;
use App\Model\Service;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ProjectController extends Controller
{
    public function index(){

        $projects = GoingProject::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',7)->first();



        return view('layouts.site.new_projects', compact('projects', 'banner'));
    }

    public function detail($url_slug)
    {
        $banner = Banner::where('id',7)->first();
        $urlSlug = explode('-', $url_slug);
        $id = end($urlSlug);

        $projects = GoingProject::where('id', $id)->first();

        $galleries = GoingProjectGallery::where('del', 0)->where('goingproject_id',$id)->get();

        return view('layouts.site.new_project_detail', compact('projects', 'galleries', 'banner'));

    }
}
