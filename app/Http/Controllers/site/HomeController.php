<?php

namespace App\Http\Controllers\site;

use App\Model\Constants;
use App\Model\Contact;
use App\Model\HomeBanner;
use App\Model\News;
use App\Model\OngoingProjectCatalog;
use App\Model\Reference;
use App\Model\ReferenceCategory;
use App\Model\ReferenceLogo;
use App\Model\Service;
use App\Model\Slider;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HomeController extends Controller
{
    public function index(){

        $sliders = Slider::where('del', 0)->orderBy('row', 'asc')->get();

        $constants = Constants::get();

        $homeBanners = HomeBanner::get();

        $referenceLogos = ReferenceLogo::where('del', 0)->orderBy('row', 'asc')->get();

        $news = News::where('del', 0)->orderBy('date','desc')->get();

        $eng = array("January","February","March","April","May","June","July","August","September","October","November","December");

        $tur = array("Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık");

/**
        $categories = ReferenceCategory::where('del', 0)->orderBy('row', 'asc')->get();


        $references = [];

        foreach ($categories as $category){

            $references[$category->id] = Reference::where('del',0)->where('reference_category_id', $category->id)->limit(5)->get();

        }
        
        **/

        $services = Service::where('del',0)->orderBy('row', 'asc')->limit(3)->get(['id', 'title_tr', 'short_content_tr', 'title_en', 'short_content_en', 'title_de', 'short_content_de']);

        $catalog = OngoingProjectCatalog::first();

        $contact = Contact::first(['center_phone']);

       $references = Reference::select('reference.id','reference.image_url', 'reference.reference_tr', 'reference.reference_en', 'reference.location',
            'reference.employer', 'reference.note', 'reference_category.reference_category_tr', 'reference_category.reference_category_en')
            ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
            ->where('reference_category.del', 0)
            ->where('reference.del', 0)
            ->inRandomOrder()->limit(8)->get();

        return view('layouts.site.home', compact('sliders', 'constants', 'homeBanners',
            'referenceLogos', 'references', 'eng', 'tur', 'news', 'services', 'catalog', 'contact'));
    }
}
