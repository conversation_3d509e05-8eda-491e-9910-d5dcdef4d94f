<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\Contact;
use App\Model\Message;
use App\Model\Office;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function index(){

        $contact = Contact::first();
        
        // Aktif ofisleri sıralama numarasına göre çek
        $offices = Office::where('is_active', 1)
                         ->orderBy('sort_order', 'asc')
                         ->get();
                         
        // Ofisleri ülkelerine göre gruplandır
        $officesByCountry = [];
        foreach ($offices as $office) {
            $countryKey = trim($office->country_name_tr);
            if (!isset($officesByCountry[$countryKey])) {
                $officesByCountry[$countryKey] = [];
            }
            $officesByCountry[$countryKey][] = $office;
        }
        
        $banner = Banner::where('id',9)->first();

        return view('layouts.site.contact', compact('contact', 'banner', 'officesByCountry'));
    }

    private function message($result, array $array){

        if ($result > 0) {

            Mail::send('mail.contact', ['info' => $array], function ($message)
            {

                $message->from('<EMAIL>', 'LATEK');
                $message->subject('Latek İletişim Formu');
                $message->to('<EMAIL>');

            });


            return response()->json([1, 'Mesajınız iletildi.']);

        } else {

            return response()->json([0, 'İşlem Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }

    public function create(Request $request){

        if(is_null($request->get('form_phone'))){

            return response()->json([2,'Telefon Numarası Giriniz']);
        }

        $phone = preg_replace('#[^0-9]*#', '', $request->get('form_phone'));


        $rules1 = [
            'form_name' => 'required|max:50',
            'form_email' => 'required|max:70',
            'form_company' => 'required|max:150',
            'form_message' => 'required',
            'form_phone' => 'required|size:10',

        ];

        $data1 = [

            'form_name' => $request->get('form_name'),
            'form_email' => $request->get('form_email'),
            'form_company' => $request->get('form_company'),
            'form_message' => $request->get('form_message'),
            'form_phone' => $phone,
        ];

        $validator1 = Validator::make($data1, $rules1);

        $data1['date'] = Carbon::now();

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result = Message::insert($data1);


        return $this->message($result, $data1);
    }
}