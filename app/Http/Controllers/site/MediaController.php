<?php

namespace App\Http\Controllers\site;

use App\Model\Banner;
use App\Model\Media;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class MediaController extends Controller
{
    public function index(){

        $medias = Media::where('del', 0)->orderBy('row', 'asc')->get();

        $banner = Banner::where('id',8)->first();

        return view('layouts.site.media', compact('medias', 'banner'));
    }
}
