<?php

namespace App\Http\Controllers\cms;

use App\Model\Slider;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class SliderController extends Controller
{
    public function index(){

        return view('layouts.cms.slider.index');
    }

    public function create(Request $request){

        $rules1 = [

            'title_tr' => 'required',
            'title_en' => 'required',
            'title_de' => 'required',
            'sub_title_tr' => 'required',
            'sub_title_en' => 'required',
            'sub_title_de' => 'required'
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'sub_title_tr' => $request->get('sub_title_tr'),
            'sub_title_en' => $request->get('sub_title_en'),
            'sub_title_de' => $request->get('sub_title_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }


        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(1920, 875)->save(base_path('/upload/slider/'. $fileName));

            $data1['image_url'] = $fileName;

            $result =   Slider::insert($data1);


            if ($result > 0) {

                return response()->json([1, 'Slider Başarıyla Eklendi.']);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else {

            return response()->json([2, 'Lütfen Resim Seçiniz!Resimlerin Boyutunu Küçülterek Yükleyiniz!']);
        }

    }

    public function tableView(){

        return view('layouts.cms.slider.table');
    }

    public function table(){

        $slider= Slider::where('del', 0)->get();

        return Datatables::of($slider)->addColumn('action', function ($slider) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $slider->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                                href="' . url("/home/<USER>/" . $slider->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';
        })->make(true);

    }

    public function edit($id){

        $slider = Slider::where('id', $id)->first();

        return view('layouts.cms.slider.edit', compact('slider'));
    }

    public function update(Request $request){

        $rules1 = [
            'title_tr' => 'required',
            'title_en' => 'required',
            'title_de' => 'required',
            'sub_title_tr' => 'required',
            'sub_title_en' => 'required',
            'sub_title_de' => 'required'
        ];

        $data1 = [
            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'sub_title_tr' => $request->get('sub_title_tr'),
            'sub_title_en' => $request->get('sub_title_en'),
            'sub_title_de' => $request->get('sub_title_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            $data1['image_url'] = $fileName;

            Image::make($image)->resize(1920, 875)->save(base_path('/upload/slider/'. $fileName));

            $result =   Slider::where('id', $request->get('slider_id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Slider Başarıyla Güncellendi.', $data1['image_url'] ]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else{

            $result =   Slider::where('id', $request->get('slider_id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Slider Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }
        }
    }

    public function delete($id){

        Slider::where('id', $id)->update(['del' => 1]);

        return back();
    }

    public function orderView(){

        return view('layouts.cms.slider.order');
    }

    public function frame(){

        $sliderInfo  = Slider::select(['id', 'image_url'])->where('del', 0)->orderby('row', 'asc')->get();

        return view('layouts.cms.slider.order_frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            Slider::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }
}
