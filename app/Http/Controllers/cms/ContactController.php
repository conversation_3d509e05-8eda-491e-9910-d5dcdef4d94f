<?php

namespace App\Http\Controllers\cms;

use App\Model\Contact;
use App\Model\SocialMedia;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function index(){

        $contact = Contact::first();

        return view('layouts.cms.contact.edit', compact('contact'));
    }

    public function update(Request $request){

        $phone1 = preg_replace('#[^0-9]*#', '', $request->get('center_phone'));
        $phone2 = preg_replace('#[^0-9]*#', '', $request->get('iz_phone'));
        $phone3 = preg_replace('#[^0-9]*#', '', $request->get('management_phone'));
        $fax1 = preg_replace('#[^0-9]*#', '', $request->get('center_fax'));
        $fax2 = preg_replace('#[^0-9]*#', '', $request->get('iz_fax'));
        $fax3 = preg_replace('#[^0-9]*#', '', $request->get('management_fax'));

        $rules1 = [
            'center_email' => 'required|max:70|email',
            'center_fax' => 'required|size:10',
            'center_address' => 'required|max:255',
'center_address_en' => 'required|max:255',
            'center_phone' => 'required|size:10',
            'iz_email' => 'required|max:70|email',
            'iz_fax' => 'required|size:10',
            'iz_address' => 'required|max:255',
 'iz_address_en' => 'required|max:255',
            'iz_phone' => 'required|size:10',
            'management_email' => 'nullable|max:70|email',
            'management_fax' => 'nullable|size:10',
            'management_address' => 'nullable|max:255',
 'management_address_en' => 'nullable|max:255',
            'management_phone' => 'nullable|size:10',
            'company' => 'nullable|max:255',
'company_en' => 'nullable|max:255',
'company_de' => 'nullable|max:255',


        ];

        $data1 = [
            'center_email' => $request->get('center_email'),
            'center_address' => $request->get('center_address'),
'center_address_en' => $request->get('center_address_en'),
'center_address_de' => $request->get('center_address_de'),
            'center_phone' => $phone1,
            'center_fax' => $fax1,
            'iz_email' => $request->get('iz_email'),
            'iz_address' => $request->get('iz_address'),
'iz_address_en' => $request->get('iz_address_en'),
'iz_address_de' => $request->get('iz_address_de'),
            'iz_phone' => $phone2,
            'iz_fax' => $fax2,
            'management_email' => $request->get('management_email'),
            'management_address' => $request->get('management_address'),
 'management_address_en' => $request->get('management_address_en'),
'management_address_de' => $request->get('management_address_de'),
            'management_phone' => $phone3,
            'management_fax' => $fax3,
            'company' => $request->get('company'),
 'company_en' => $request->get('company_en'),
'company_de' => $request->get('company_de'),


        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result = Contact::where('id', $request->get('id'))->update([
            'center_email' => $request->get('center_email'),
            'center_address' => $request->get('center_address'),
'center_address_en' => $request->get('center_address_en'),
'center_address_de' => $request->get('center_address_de'),

            'center_phone' => $request->get('center_phone'),
            'center_fax' => $request->get('center_fax'),
            'iz_email' => $request->get('iz_email'),
            'iz_address' => $request->get('iz_address'),
'iz_address_en' => $request->get('iz_address_en'),
'iz_address_de' => $request->get('iz_address_de'),

            'iz_phone' => $request->get('iz_phone'),
            'iz_fax' => $request->get('iz_fax'),
            'management_email' => $request->get('management_email'),
            'management_address' => $request->get('management_address'),
 'management_address_en' => $request->get('management_address_en'),
'management_address_de' => $request->get('management_address_de'),

            'management_phone' => $request->get('management_phone'),
            'management_fax' => $request->get('management_fax'),
            'company' => $request->get('company'),
'company_en' => $request->get('company_en'),
'company_de' => $request->get('company_de')


        ]);

        if ($result > 0) {

            return response()->json([1, 'İletişim Bilgileri Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }
    }

    public function socialMediaView(){
        $socialMedia = SocialMedia::get();
        return view('layouts.cms.contact.socialmedia', compact('socialMedia'));
    }

    public function socialMediaUpdate(Request $request){

        $rules1 = [

            'instagram' => 'nullable|max:255',
            'facebook' => 'nullable|max:255',
            'twitter' => 'nullable|max:255',
            'youtube' => 'nullable|max:255',
            'linkedin' => 'nullable|max:255',
            'google' => 'nullable|max:255'

        ];

        $data1 = [
            'instagram' => $request->get('instagram'),
            'facebook' => $request->get('facebook'),
            'twitter' => $request->get('twitter'),
            'youtube' => $request->get('youtube'),
            'linkedin' => $request->get('linkedin'),
            'google' => $request->get('google')

        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result = SocialMedia::where('id', $request->get('id'))->update($data1);


        if ($result > 0) {

            return response()->json([1, 'Sosyal Medya Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }
}