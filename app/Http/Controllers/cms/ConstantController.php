<?php

namespace App\Http\Controllers\cms;

use App\Model\Constants;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class ConstantController extends Controller
{
    public function index(){

        $constant = Constants::first();

        return view('layouts.cms.constant.index', compact( 'constant'));
    }

    public function update(Request $request){

        $rules1 = [
            'title1_tr'  => 'required|max:150',
            'title1_en'  => 'required|max:150',
            'title1_de'  => 'required|max:150',
            'title2_tr'  => 'required|max:150',
            'title2_en'  => 'required|max:150',
            'title2_de'  => 'required|max:150',
            'content1_tr'  => 'required|max:255',
            'content1_en'  => 'required|max:255',
            'content1_de'  => 'required|max:255',
            'content2_tr'  => 'required|max:255',
            'content2_en'  => 'required|max:255',
            'content2_de'  => 'required|max:255'
        ];

        $data1 = [
            'title1_tr'  => $request->get('title1_tr'),
            'title1_en'  => $request->get('title1_en'),
            'title1_de'  => $request->get('title1_de'),
            'title2_tr'  => $request->get('title2_tr'),
            'title2_en'  => $request->get('title2_en'),
            'title2_de'  => $request->get('title2_de'),
            'content1_tr'  => $request->get('content1_tr'),
            'content1_en'  => $request->get('content1_en'),
            'content1_de'  => $request->get('content1_de'),
            'content2_tr'  => $request->get('content2_tr'),
            'content2_en'  => $request->get('content2_en'),
            'content2_de'  => $request->get('content2_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result =  Constants::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'Anasayfa Sabit Yazılar Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }


    }
}
