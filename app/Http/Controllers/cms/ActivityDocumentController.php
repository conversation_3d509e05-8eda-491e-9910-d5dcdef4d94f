<?php

namespace App\Http\Controllers\cms;

use App\Model\ActivityDocument;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Intervention\Image\Facades\Image;

class ActivityDocumentController extends Controller
{

    public function index(){

        return view('layouts.cms.corporate.activity_doc.index');
    }

    public function create(Request $request){

        if ( $request->hasFile('image1') && $request->hasFile('image2') && $request->hasFile('image3')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');
            $image3 = $request->file('image3');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());
            $fileExtension3 = mb_strtolower($image3->getClientOriginalExtension());

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;
            $fileName3 = sha1(uniqid()) . '.' . $fileExtension3;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName2));
            Image::make($image3)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName3));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;
            $data1['image_url2'] = $fileName3;
            $data1['title_tr'] = $request->get('title_tr');
            $data1['title_en'] = $request->get('title_en');
            $data1['title_de'] = $request->get('title_de');

            $result = ActivityDocument::insert($data1);

            if ($result > 0){

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Eklendi.']);

            }else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else {

            return response()->json([2, 'Lütfen Resimleri Eksiksiz Seçiniz!Resimlerin Boyutunu Küçülterek Yükleyiniz!(Max:1MB)']);
        }



    }

    public function edit($id){

        $activity = ActivityDocument::where('id', $id)->first();

        return view('layouts.cms.corporate.activity_doc.edit', compact('activity'));
    }

    public function update(Request $request){
        
        $data1 = [];
        
        if ($request->has('title_tr')) {
            $data1['title_tr'] = $request->get('title_tr');
        }
        
        if ($request->has('title_en')) {
            $data1['title_en'] = $request->get('title_en');
        }
        
        if ($request->has('title_de')) {
            $data1['title_de'] = $request->get('title_de');
        }

        if ($request->hasFile('image1') && $request->hasFile('image2') && $request->hasFile('image3')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');
            $image3 = $request->file('image3');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());
            $fileExtension3 = mb_strtolower($image3->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }
            
            if (!in_array($fileExtension3, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;
            $fileName3 = sha1(uniqid()) . '.' . $fileExtension3;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName2));
            Image::make($image3)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName3));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;
            $data1['image_url2'] = $fileName3;

            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0){
                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', $data1['image_url'], $data1['image_url1'], $data1['image_url2']]);
            } else {
                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        } else if($request->hasFile('image1') && $request->hasFile('image2')) {
            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName2));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;

            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0){
                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', $data1['image_url'], $data1['image_url1']]);
            } else {
                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }
        } else if($request->hasFile('image1')){

            $image = $request->file('image1');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName));

            $data1['image_url'] = $fileName;

            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url" => $data1['image_url'])]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if($request->hasFile('image2')){

            $image = $request->file('image2');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName));

            $data1['image_url1'] = $fileName;

            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url1" => $data1['image_url1'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if($request->hasFile('image3')){

            $image = $request->file('image3');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/activity_doc/'. $fileName));

            $data1['image_url2'] = $fileName;

            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url2" => $data1['image_url2'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if (!empty($data1)) {
            // Sadece başlık alanları güncelleniyor
            $result = ActivityDocument::where('id', $request->get('id'))->update($data1);
            
            if ($result > 0) {
                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.']);
            } else {
                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }
        } else {
            return response()->json([0, 'Hiçbir değişiklik yapılmadı.']);
        }

    }

    public function table(){
        try {
            $gallery = ActivityDocument::where('del', 0)->get();

            return Datatables::of($gallery)
                ->editColumn('image_url', function ($gallery) {
                    return '<img src="/upload/activity_doc/'.$gallery->image_url.'" style="width: 150px;" alt="" />';
                })
                ->editColumn('image_url1', function ($gallery) {
                    return '<img src="/upload/activity_doc/'.$gallery->image_url1.'" style="width: 150px;" alt="" />';
                })
                ->editColumn('image_url2', function ($gallery) {
                    if ($gallery->image_url2) {
                        return '<img src="/upload/activity_doc/'.$gallery->image_url2.'" style="width: 150px;" alt="" />';
                    }
                    return '';
                })
                ->addColumn('action', function ($gallery) {
                    return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                             
                             <li><a
                                href="' . url("/home/<USER>/" . $gallery->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                                href="' . url("/home/<USER>/" . $gallery->id . "") . '">Sil</a></li>
                                
                            </ul>
                        </div>';
                })
                ->rawColumns(['image_url', 'image_url1', 'image_url2', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function delete($id){

        ActivityDocument::where('id', $id)->update(['del' => 1]);

        return back();

    }

    public function frame(){

        $sliderInfo  = ActivityDocument::where('del', 0)->orderBy('row', 'asc')->get();

        return view('layouts.cms.corporate.activity_doc.frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            ActivityDocument::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }

}
