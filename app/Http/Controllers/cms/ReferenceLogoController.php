<?php

namespace App\Http\Controllers\cms;

use App\Model\ReferenceLogo;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Intervention\Image\Facades\Image;

class ReferenceLogoController extends Controller
{
    public function indexGallery(){

        return view('layouts.cms.reference_logo.index');
    }

    public function create(Request $request){

        if(!$request->file('image')){

            return response()->json("2");
        }

        $status = 0;

        for ($i = 0; $i < count($request->file('image')); $i++) {

            if (isset($request->file('image')[$i])) {

                $image = $request->file('image')[$i];

                $fileExtension = mb_strtolower($image->getClientOriginalExtension());

                $allowed = array('png', 'jpg', 'jpeg');

                if (!in_array($fileExtension, $allowed)) {

                    return response()->json('0');
                }

                $fileName = sha1(uniqid()) . '.' . $fileExtension;


                Image::make($image)->resize(220, 120)->save(base_path('/upload/reference_logo/' . $fileName));


                $result = ReferenceLogo::create([

                    'image_url' => $fileName,

                ]);


                if ($result->count() > 0) {

                    $status++;

                } else {

                    return response()->json("0");
                }


            } else {

                return response()->json('0');
            }

        }

        return response()->json('1');

    }

    public function table(){

        $gallery = ReferenceLogo::where('del', 0)->get();

        return Datatables::of($gallery)->addColumn('action', function ($gallery) {

            return '<div class="btn-group">
                        <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                            <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                        </button>

                        <ul class="dropdown-menu">
                         
                            <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                            href="' . url("/home/<USER>/" . $gallery->id . "") . '">Sil</a></li>
                        </ul>
                    </div>';
        })->make(true);
    }

    public function delete($id){

        ReferenceLogo::where('id', $id)->update(['del' => 1]);

        return back();

    }

    public function frame(){

        $sliderInfo  = ReferenceLogo::select(['id', 'image_url'])->where('del', 0)->orderby('row', 'asc')->get();

        return view('layouts.cms.reference_logo.frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            ReferenceLogo::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }
}
