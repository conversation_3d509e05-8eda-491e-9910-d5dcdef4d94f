<?php

namespace App\Http\Controllers\cms;

use App\Model\Message;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class MessageController extends Controller
{
    public function messagesTable(){

        return view('layouts.cms.message.table');
    }

    public function messagesTableGet(){

        $messages = Message::select(['id', 'form_name', 'form_email', 'date'])->where('del', 0)->get();

        return Datatables::of($messages)->addColumn('action', function ($messages) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $messages->id . "") . '">Görüntüle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $messages->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);
    }

    public function messagesview($id){

        $message = Message::where('id', $id)->first();

        return view('layouts.cms.message.singlemessage', compact('message'));
    }

    public function messageDelete($id){

        Message::where('id', $id)->update([

            'del' => 1
        ]);
        return back();

    }
}
