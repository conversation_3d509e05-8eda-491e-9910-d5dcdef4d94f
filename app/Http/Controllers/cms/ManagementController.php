<?php

namespace App\Http\Controllers\cms;

use App\Model\Management;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class ManagementController extends Controller
{

    public function index(){
        return view('layouts.cms.corporate.management.index');
    }

    public function create(Request $request){

        $rules1 = [

            'management_name' => 'required|max:100',
            'task_tr' => 'required|max:255',
            'task_en' => 'required|max:255',
            'task_de' => 'required|max:255',
            'univercity_tr' => 'required|max:255',
            'univercity_en' => 'required|max:255',
            'univercity_de' => 'required|max:255',
            'email' => 'max:150',
            'twitter' => 'max:150',
            'google' => 'max:150',
            'instagram' => 'max:150',
            'facebook' => 'max:150',
            'linkedin' => 'max:150',
            'youtube' => 'max:150',
            'pinterest' => 'max:150'
        ];

        $data1 = [
            'management_name' => $request->get('management_name'),
            'task_tr' => $request->get('task_tr'),
            'task_en' => $request->get('task_en'),
            'task_de' => $request->get('task_de'),
            'univercity_tr' => $request->get('univercity_tr'),
            'univercity_en' => $request->get('univercity_en'),
            'univercity_de' => $request->get('univercity_de'),
            'email' => $request->get('email'),
            'twitter' => $request->get('twitter'),
            'google' => $request->get('google'),
            'instagram' => $request->get('instagram'),
            'facebook' => $request->get('facebook'),
            'linkedin' => $request->get('linkedin'),
            'youtube' => $request->get('youtube'),
            'pinterest' => $request->get('pinterest')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return back();
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(270,285)->save(base_path('/upload/management/' . $fileName));

            $data1["image_url"] = $fileName;

            $result = Management::insert($data1);

            if ($result > 0) {

                return response()->json([1, 'Üye Yönetim Kadrosuna Başarıyla Eklendi.']);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else {
            return response()->json([0, 'Lütfen Resim Seçiniz!']);

        }


    }

    public function tableView(){
        return view('layouts.cms.corporate.management.table');
    }

    public function table(){

        $about = Management::select(['management_name', 'task_tr', 'image_url', 'id', 'email'])->where('del', 0)->get();
        return Datatables::of($about)->addColumn('action', function ($about) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $about->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $about->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);
    }

    public function edit($id){

        $member = Management::where('id', $id)->first();

        return view('layouts.cms.corporate.management.edit', compact('member'));
    }

    public function update(Request $request){

        $rules1 = [
            'management_name' => 'required|max:100',
            'task_tr' => 'required|max:255',
            'task_en' => 'required|max:255',
            'task_de' => 'required|max:255',
            'univercity_tr' => 'required|max:255',
            'univercity_en' => 'required|max:255',
            'univercity_de' => 'required|max:255',
            'email' => 'max:150',
            'twitter' => 'max:150',
            'google' => 'max:150',
            'instagram' => 'max:150',
            'facebook' => 'max:150',
            'linkedin' => 'max:150',
            'youtube' => 'max:150',
            'pinterest' => 'max:150'
        ];

        $data1 = [
            'management_name' => $request->get('management_name'),
            'task_tr' => $request->get('task_tr'),
            'task_en' => $request->get('task_en'),
            'task_de' => $request->get('task_de'),
            'univercity_tr' => $request->get('univercity_tr'),
            'univercity_en' => $request->get('univercity_en'),
            'univercity_de' => $request->get('univercity_de'),
            'email' => $request->get('email'),
            'twitter' => $request->get('twitter'),
            'google' => $request->get('google'),
            'instagram' => $request->get('instagram'),
            'facebook' => $request->get('facebook'),
            'linkedin' => $request->get('linkedin'),
            'youtube' => $request->get('youtube'),
            'pinterest' => $request->get('pinterest')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }


        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return back();
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(270,285)->save(base_path('/upload/management/' . $fileName));

            $data1["image_url"] = $fileName;

            $result = Management::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Yönetim Kadrosu Üyesi Başarıyla Güncellendi.', $data1["image_url"]]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else {

            $result = Management::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Yönetim Kadrosu Üyesi Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }

    }

    public function sortView(){

        return view('layouts.cms.corporate.management.sort');
    }

    public function frame(){

        $aboutMember =  Management::select(['image_url', 'management_name', 'id', 'row'])->where('del', 0)->orderBy('row', 'asc')->get();

        return view('layouts.cms.corporate.management.frame')->with('aboutMember', $aboutMember);
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            Management::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");


    }


    public function delete($id){

        Management::where('id', $id)->update(['del' => 1]);

        return back();
    }
}
