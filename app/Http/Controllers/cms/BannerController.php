<?php

namespace App\Http\Controllers\cms;

use App\Model\Banner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class BannerController extends Controller
{
    public function index(){

        $banner = Banner::select(['name', 'id'])->get();

        return view('layouts.cms.banner.edit', compact('banner'));

    }

    public function bannerGet(Request $request){

        if($request->get('id') == 0){

            return response()->json("500");
        }

        return response()->json(Banner::where('id', $request->get('id') )->first());

    }

    private function messageUpdate($result){

        if ($result > 0) {

            return response()->json([1, 'Banner Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }

    public function update(Request $request){


        if(is_null($request->get('category'))){

            return response()->json([3, 'Lütfen Kategori Seçiniz!']);
        }

        $rules1 = [
            'title_tr' => 'required|max:100',
            'title_en' => 'required|max:100',
            'title_de' => 'required|max:100'
        ];

        $data1 = [
            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            $data1['image_url'] = $fileName;

            Image::make($image)->resize(1920, 528)->save(base_path('/upload/banner/'. $fileName));

            $result = Banner::where('id', $request->get('banner_id'))->update($data1);

            return $this->messageUpdate($result);


        } else{

            $result = Banner::where('id', $request->get('banner_id'))->update($data1);

            return $this->messageUpdate($result);
        }

    }
}
