<?php

namespace App\Http\Controllers\cms;

use App\Model\PageMeta;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class MetaController extends Controller
{
    public function index(){

        $pageMeta = PageMeta::first();

        return view('layouts.cms.pagemeta.index', compact('pageMeta'));
    }

    public function update(Request $request){

        $rules1 = [
            'title' => 'required|max:255',
            'description' => 'required',
            'keywords' => 'required',
        ];

        $data1 = [

            'title' => $request->get('title'),
            'description' => $request->get('desc'),
            'keywords' => $request->get('keyword')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }


        $result = PageMeta::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'Site Ayarları Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }
}
