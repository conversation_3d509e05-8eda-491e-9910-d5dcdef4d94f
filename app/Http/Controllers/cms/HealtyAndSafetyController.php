<?php

namespace App\Http\Controllers\cms;

use App\Model\HealtyAndSafety;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class HealtyAndSafetyController extends Controller
{
    public function edit(){

        $healty = HealtyAndSafety::first();

        return view('layouts.cms.corporate.healty_safety.index', compact('healty'));
    }

    public function update(Request $request){


        $rules1 = [

            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required'
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }


        $result = HealtyAndSafety::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'İş Sağlığı ve Güvenliği Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }




    }


}
