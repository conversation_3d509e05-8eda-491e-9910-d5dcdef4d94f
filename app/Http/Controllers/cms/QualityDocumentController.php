<?php

namespace App\Http\Controllers\cms;

use App\Model\QualityDocument;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Intervention\Image\Facades\Image;

class QualityDocumentController extends Controller
{
    public function index(){

        return view('layouts.cms.corporate.quality_doc.index');
    }

    public function create(Request $request){

        if ($request->hasFile('image1') && $request->hasFile('image2') && $request->hasFile('image3')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');
            $image3 = $request->file('image3');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());
            $fileExtension3 = mb_strtolower($image3->getClientOriginalExtension());

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;
            $fileName3 = sha1(uniqid()) . '.' . $fileExtension3;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName2));
            Image::make($image3)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName3));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;
            $data1['image_url2'] = $fileName3;

            $result = QualityDocument::insert($data1);

            if ($result > 0){

                return response()->json([1, 'Kalite Belgesi Başarıyla Eklendi.']);

            }else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else {

            return response()->json([2, 'Lütfen Resimleri Eksiksiz Seçiniz!Resimlerin Boyutunu Küçülterek Yükleyiniz!(Max:1MB)']);
        }



    }

    public function edit($id){

        $quality = QualityDocument::where('id', $id)->first();

        return view('layouts.cms.corporate.quality_doc.edit', compact('quality'));
    }

    public function update(Request $request){


        if ($request->hasFile('image1') && $request->hasFile('image2') && $request->hasFile('image3')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');
            $image3 = $request->file('image3');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());
            $fileExtension3 = mb_strtolower($image3->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension3, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;
            $fileName3 = sha1(uniqid()) . '.' . $fileExtension3;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName2));
            Image::make($image3)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName3));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;
            $data1['image_url2'] = $fileName3;

            $result = QualityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0){

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', $data1['image_url'], $data1['image_url1'], $data1['image_url2']]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if ($request->hasFile('image1') && $request->hasFile('image2')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {
                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName1));
            Image::make($image2)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName2));

            $data1['image_url'] = $fileName1;
            $data1['image_url1'] = $fileName2;

            $result = QualityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0){

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', $data1['image_url'], $data1['image_url1']]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        } else if($request->hasFile('image1')){

            $image = $request->file('image1');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName));

            $data1['image_url'] = $fileName;

            $result = QualityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url" => $data1['image_url'])]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if($request->hasFile('image2')){

            $image = $request->file('image2');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName));

            $data1['image_url1'] = $fileName;

            $result = QualityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url1" => $data1['image_url1'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else if($request->hasFile('image3')){

            $image = $request->file('image3');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(450, 650)->save(base_path('/upload/quality_doc/'. $fileName));

            $data1['image_url2'] = $fileName;

            $result = QualityDocument::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Faaliyet Belgesi Başarıyla Güncellendi.', array("image_url2" => $data1['image_url2'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else {

            return response()->json([0, 'Lütfen Resim Seçiniz!Resimlerin Boyutunu Küçülterek Yükleyiniz!(Max:1MB)']);

        }

    }

    public function table(){

        $gallery = QualityDocument::where('del', 0)->get();

        return Datatables::of($gallery)->addColumn('action', function ($gallery) {

            return '<div class="btn-group">
                        <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                            <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                        </button>

                        <ul class="dropdown-menu">
                        
                         <li><a
                            href="' . url("/home/<USER>/" . $gallery->id . "") . '">Düzenle</a></li>
                            <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                            href="' . url("/home/<USER>/" . $gallery->id . "") . '">Sil</a></li>
                        </ul>
                    </div>';
        })->make(true);
    }

    public function delete($id){

        QualityDocument::where('id', $id)->update(['del' => 1]);

        return back();

    }

    public function frame(){

        $sliderInfo  = QualityDocument::where('del', 0)->orderBy('row', 'asc')->get();

        return view('layouts.cms.corporate.quality_doc.frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            QualityDocument::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }
}
