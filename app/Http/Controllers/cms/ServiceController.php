<?php

namespace App\Http\Controllers\cms;

use App\Model\Service;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class ServiceController extends Controller
{
    public function index(){

        return view('layouts.cms.service.index');
    }

    public function create(Request $request){

        $rules1 = [

            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'short_content_tr' => 'required|max:255',
            'short_content_en' => 'required|max:255',
            'short_content_de' => 'required|max:255',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required',
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'short_content_tr' => $request->get('short_content_tr'),
            'short_content_en' => $request->get('short_content_en'),
            'short_content_de' => $request->get('short_content_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }
        
        $result = Service::insert($data1);

        if ($result > 0){

            return response()->json([1, 'Hizmet Başarıyla Eklendi.']);

        }else {

            return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

/**
        if ( $request->hasFile('image1') && $request->hasFile('image2')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(506, 375)->save(base_path('/upload/service/'. $fileName1));
            Image::make($image2)->resize(326, 340)->save(base_path('/upload/service/'. $fileName2));

            $data1['image1'] = $fileName1;
            $data1['image2'] = $fileName2;

            $result = Service::insert($data1);

            if ($result > 0){

                return response()->json([1, 'Hizmet Başarıyla Eklendi.']);

            }else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        } else {

            return response()->json([2, 'Lütfen Resimleri Eksiksiz Seçiniz!Resimlerin Boyutunu Küçülterek Yükleyiniz!(Max:1MB)']);
        }
        
        **/

    }

    public function tableView(){

        return view('layouts.cms.service.table');
    }

    public function table(){

        $match = Service::select(['title_tr', 'title_en', 'title_de', 'id'])
            ->where('del', 0)->get();

        return Datatables::of($match)->addColumn('action', function ($match) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $match->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                                href="' . url("/home/<USER>/" . $match->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';
        })->make(true);

    }

    public function edit($id){

        $project = Service::where('id', $id)->first();

        return view('layouts.cms.service.edit', compact('project'));
    }

    public function update(Request $request){

        $rules1 = [

            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'short_content_tr' => 'required|max:255',
            'short_content_en' => 'required|max:255',
            'short_content_de' => 'required|max:255',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required',
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'short_content_tr' => $request->get('short_content_tr'),
            'short_content_en' => $request->get('short_content_en'),
            'short_content_de' => $request->get('short_content_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }
        
        $result = Service::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'Hizmet Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

/**
        if ($request->hasFile('image1') && $request->hasFile('image2')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(506, 375)->save(base_path('/upload/service/'. $fileName1));
            Image::make($image2)->resize(326, 340)->save(base_path('/upload/service/'. $fileName2));

            $data1['image1'] = $fileName1;
            $data1['image2'] = $fileName2;

            $result = Service::where('id', $request->get('id'))->update($data1);

            if ($result > 0){

                return response()->json([1, 'Hizmet Başarıyla Güncellendi.', $data1['image1'], $data1['image2']]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image1')){

            $image = $request->file('image1');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(506, 375)->save(base_path('/upload/service/'. $fileName));

            $data1['image1'] = $fileName;

            $result = Service::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hizmet Başarıyla Güncellendi.', array("image1" => $data1['image1'])]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image2')){



            $image = $request->file('image2');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(326, 340)->save(base_path('/upload/service/'. $fileName));

            $data1['image2'] = $fileName;

            $result = Service::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hizmet Başarıyla Güncellendi.', array("image2" => $data1['image2'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else{


            $result = Service::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hizmet Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }
        }
        
        **/


    }

    public function delete($id){

        Service::where('id', $id)->update(['del' => 1]);

        return back();
    }

    public function orderView(){

        return view('layouts.cms.service.order');
    }

    public function frame(){

        $sliderInfo  = Service::select(['id', 'title_tr', 'title_en', 'title_de'])->where('del', 0)->orderby('row', 'asc')->get();

        return view('layouts.cms.service.order_frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            Service::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }
}
