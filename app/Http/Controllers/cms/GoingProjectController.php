<?php

namespace App\Http\Controllers\cms;

use App\Model\GoingProject;
use App\Model\GoingProjectGallery;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class GoingProjectController extends Controller
{
    public function index(){

        return view('layouts.cms.goingproject.index');
    }

    public function create(Request $request){

        $rules1 = [
            'project'  => 'required|max:150',
            'project_en'  => 'required|max:150',
            'project_de'  => 'required|max:150',
            'location'  => 'nullable|max:150',
            'location_en'  => 'nullable|max:150',
            'location_de'  => 'nullable|max:150',
            'employer' => 'nullable|max:150',
            'employer_en' => 'nullable|max:150',
            'employer_de' => 'nullable|max:150',
            'note' => 'nullable|max:150',
            'note_en' => 'nullable|max:150',
            'note_de' => 'nullable|max:150'
        ];

        $data1 = [
            'project'  => $request->get('project'),
            'project_en'  => $request->get('project_en'),
            'project_de'  => $request->get('project_de'),
            'location'  => $request->get('location'),
            'location_en'  => $request->get('location_en'),
            'location_de'  => $request->get('location_de'),
            'employer' => $request->get('employer'),
            'employer_en' => $request->get('employer_en'),
            'employer_de' => $request->get('employer_de'),
            'note' => $request->get('note'),
            'note_en' => $request->get('note_en'),
            'note_de' => $request->get('note_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');


            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(790, 540)->save(base_path('/upload/goingproject/' . $fileName));

            $data1['image_url'] = $fileName;

            $result = GoingProject::insert($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Proje Başarıyla Eklendi.']);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {
            return response()->json([2, 'Lütfen Resimleri Seçiniz!']);

        }


    }

    public function tableView(){

        return view('layouts.cms.goingproject.table');
    }

    public function table(){

        $projects = GoingProject::where('del', 0)->get();

        return Datatables::of($projects)->addColumn('action', function ($projects) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $projects->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $projects->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);

    }

    public function edit($id){


        $project= GoingProject::where('id', $id)->first();

        return view('layouts.cms.goingproject.edit', compact( 'project'));
    }

    public function update(Request $request){

        $rules1 = [
            'project'  => 'required|max:150',
            'project_en'  => 'required|max:150',
            'project_de'  => 'required|max:150',
            'location'  => 'nullable|max:150',
            'location_en'  => 'nullable|max:150',
            'location_de'  => 'nullable|max:150',
            'employer' => 'nullable|max:150',
            'employer_en' => 'nullable|max:150',
            'employer_de' => 'nullable|max:150',
            'note' => 'nullable|max:150',
            'note_en' => 'nullable|max:150',
            'note_de' => 'nullable|max:150'
        ];

        $data1 = [
            'project'  => $request->get('project'),
            'project_en'  => $request->get('project_en'),
            'project_de'  => $request->get('project_de'),
            'location'  => $request->get('location'),
            'location_en'  => $request->get('location_en'),
            'location_de'  => $request->get('location_de'),
            'employer' => $request->get('employer'),
            'employer_en' => $request->get('employer_en'),
            'employer_de' => $request->get('employer_de'),
            'note' => $request->get('note'),
            'note_en' => $request->get('note_en'),
            'note_de' => $request->get('note_de')
        ];


        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');


            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(790, 540)->save(base_path('/upload/goingproject/' . $fileName));

            $data1['image_url'] = $fileName;

            $result = GoingProject::where('id',$request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Proje Başarıyla Güncellendi.', $data1['image_url']]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {

            $result = GoingProject::where('id',$request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Proje Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }


    }

    public function delete($id){

        GoingProject::where('id', $id)->update([
            'del' => 1
        ]);

        return back();
    }

    public function orderView(){

        return view('layouts.cms.goingproject.order');
    }

    public function frame(){

        $sliderInfo  = GoingProject::select(['id', 'image_url', 'project', 'project_en', 'project_de'])->where('del', 0)->orderby('row', 'asc')->get();

        return view('layouts.cms.goingproject.order_frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            GoingProject::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }


    public function gallery(){

        $projects = GoingProject::select(['project', 'id'])->where('del', 0)->get();

        return view('layouts.cms.goingproject.gallery', compact('projects'));
    }

    public function galleryCreate(Request $request){

        if($request->get('goingproject_id') ==0){

            return response()->json("2");
        }
        $status = 0;

        for ($i = 0; $i < count($request->file('gallery')); $i++) {

            if (isset($request->file('gallery')[$i])) {

                $image = $request->file('gallery')[$i];

                $fileExtension = mb_strtolower($image->getClientOriginalExtension());

                $allowed = array('png', 'jpg', 'jpeg');

                if (!in_array($fileExtension, $allowed)) {

                    return response()->json('0');
                }

                $fileName = sha1(uniqid()) . '.' . $fileExtension;


                Image::make($image)->resize(720, 640)->save(base_path('/upload/goingproject_gallery/' . $fileName));

                $result = GoingProjectGallery::create([

                    'goingproject_id' => $request->get('goingproject_id'),
                    'image_url' => $fileName,

                ]);

                $count = count(json_decode($result, true));

                if ($count > 0) {

                    $status++;

                } else {

                    return response()->json("0");
                }


            } else {

                return response()->json('0');
            }


        }
        return response()->json(true);
    }

    public function galleryDeleteView(){

        return view('layouts.cms.goingproject.gallerytableview');
    }

    public function galleryEditTable(){

        $reference = GoingProject::select(['goingproject_gallery.id', 'goingprojects.project', 'goingproject_gallery.image_url'])
            ->join('goingproject_gallery', 'goingprojects.id', '=', 'goingproject_gallery.goingproject_id')
            ->where('goingproject_gallery.del', 0)
            ->where('goingprojects.del', 0)->get();


        return Datatables::of($reference)->addColumn('action', function ($reference){

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu"> 
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $reference->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);
    }

    public function galleryDelete($id){

        GoingProjectGallery::where('id', $id)->update([

            'del' => 1
        ]);
        return back();
    }
}