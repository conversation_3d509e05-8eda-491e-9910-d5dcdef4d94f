<?php

namespace App\Http\Controllers\cms;

use App\Model\Hr;
use App\Model\HrContent;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class HrController extends Controller
{
    public function index(){

        $content = HrContent::first();

        return view('layouts.cms.corporate.hr.index', compact( 'content'));
    }

    public function update(Request $request){

        $rules1 = [

            'title_tr'  => 'required|max:150',
            'title_en'  => 'required|max:150',
            'title_de'  => 'required|max:150',
            'content_tr'  => 'required',
            'content_en'  => 'required',
            'content_de'  => 'required',

        ];

        $data1 = [

            'title_tr'  => $request->get('title_tr'),
            'title_en'  => $request->get('title_en'),
            'title_de'  => $request->get('title_de'),
            'content_tr'  => $request->get('content_tr'),
            'content_en'  => $request->get('content_en'),
            'content_de'  => $request->get('content_de'),

        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result =  HrContent::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'İnsan Kaynakları Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }


    }

    public function indexCv(){

        return view('layouts.cms.corporate.hr.table');
    }

    public function tableCv(){

        $hr = Hr::where('del', 0)->orderBy('date', 'desc')->get();

        return Datatables::of($hr)->addColumn('action', function ($hr){

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu"> 
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $hr->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);
    }

    public function deleteCv($id){

        Hr::where('id', $id)->update(['del'=> 1]);

        return back();
    }


}
