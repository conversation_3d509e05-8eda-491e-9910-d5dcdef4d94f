<?php

namespace App\Http\Controllers\cms;

use App\Model\Office;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;

class OfficeController extends Controller
{
    public function index()
    {
        $offices = Office::orderBy('sort_order', 'asc')->get();
        return view('layouts.cms.contact.offices.index', compact('offices'));
    }

    public function create()
    {
        return view('layouts.cms.contact.offices.create');
    }

    public function store(Request $request)
    {
        $phone = preg_replace('#[^0-9]*#', '', $request->get('phone'));
        $fax = preg_replace('#[^0-9]*#', '', $request->get('fax'));

        $rules = [
            'country_name_tr' => 'required|max:255',
            'country_name_en' => 'required|max:255',
            'country_name_de' => 'required|max:255',
            'office_name_tr' => 'required|max:255',
            'office_name_en' => 'required|max:255',
            'office_name_de' => 'required|max:255',
            'phone' => 'nullable|max:20',
            'fax' => 'nullable|max:20',
            'email' => 'nullable|email|max:70',
            'gsm' => 'nullable|max:20',
            'address_tr' => 'required',
            'address_en' => 'required',
            'address_de' => 'nullable',
            'is_active' => 'nullable',
            'sort_order' => 'nullable|integer'
        ];

        $data = $request->all();
        
        if (!isset($data['is_active'])) {
            $data['is_active'] = 0;
        }
        
        if (empty($data['sort_order'])) {
            $data['sort_order'] = 0;
        }

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return response()->json([2, $validator->errors()->toArray()]);
        }

        $result = Office::create($data);

        if ($result) {
            return response()->json([1, 'Ofis başarıyla eklendi.']);
        } else {
            return response()->json([0, 'Ekleme işlemi başarısız oldu! Tekrar deneyiniz.']);
        }
    }

    public function edit($id)
    {
        $office = Office::findOrFail($id);
        return view('layouts.cms.contact.offices.edit', compact('office'));
    }

    public function update(Request $request, $id)
    {
        $phone = preg_replace('#[^0-9]*#', '', $request->get('phone'));
        $fax = preg_replace('#[^0-9]*#', '', $request->get('fax'));

        $rules = [
            'country_name_tr' => 'required|max:255',
            'country_name_en' => 'required|max:255',
            'country_name_de' => 'required|max:255',
            'office_name_tr' => 'required|max:255',
            'office_name_en' => 'required|max:255',
            'office_name_de' => 'required|max:255',
            'phone' => 'nullable|max:20',
            'fax' => 'nullable|max:20',
            'email' => 'nullable|email|max:70',
            'gsm' => 'nullable|max:20',
            'address_tr' => 'required',
            'address_en' => 'required',
            'address_de' => 'nullable',
            'is_active' => 'nullable',
            'sort_order' => 'nullable|integer'
        ];

        $data = $request->all();
        
        if (!isset($data['is_active'])) {
            $data['is_active'] = 0;
        }
        
        if (empty($data['sort_order'])) {
            $data['sort_order'] = 0;
        }

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return response()->json([2, $validator->errors()->toArray()]);
        }

        $office = Office::findOrFail($id);
        $result = $office->update($data);

        if ($result) {
            return response()->json([1, 'Ofis başarıyla güncellendi.']);
        } else {
            return response()->json([0, 'Güncelleme işlemi başarısız oldu! Tekrar deneyiniz.']);
        }
    }

    public function destroy($id)
    {
        $office = Office::findOrFail($id);
        $result = $office->delete();

        if ($result) {
            return response()->json([1, 'Ofis başarıyla silindi.']);
        } else {
            return response()->json([0, 'Silme işlemi başarısız oldu! Tekrar deneyiniz.']);
        }
    }
} 