<?php

namespace App\Http\Controllers\cms;

use App\Model\OngoingProject;
use App\Model\OngoingProjectCatalog;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class OngoingProjectController extends Controller
{
    public function edit(){

        $project = OngoingProject::first();

        return view('layouts.cms.ongoing_project.index', compact('project'));
    }

    public function update(Request $request){


        $rules1 = [

            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required'
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de')

        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image1') && $request->hasFile('image2')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(506, 375)->save(base_path('/upload/ongoing_project/'. $fileName1));
            Image::make($image2)->resize(326, 340)->save(base_path('/upload/ongoing_project/'. $fileName2));

            $data1['image1'] = $fileName1;
            $data1['image2'] = $fileName2;

            $result = OngoingProject::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Projeler Başarıyla Güncellendi.',$data1['image1'], $data1['image2']]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image1')){

            $image = $request->file('image1');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(506, 375)->save(base_path('/upload/ongoing_project/'. $fileName));

            $data1['image1'] = $fileName;

            $result = OngoingProject::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Projeler Başarıyla Güncellendi.', array("image1" => $data1['image1'])]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image2')){

            $image = $request->file('image2');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(326, 340)->save(base_path('/upload/ongoing_project/'. $fileName));

            $data1['image2'] = $fileName;

            $result = OngoingProject::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Projeler Başarıyla Güncellendi.', array("image2" => $data1['image2'])]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else{


            $result = OngoingProject::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Devam Eden Projeler Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }


    }

    public function index(){

        $catalog = OngoingProjectCatalog::first();

        return view('layouts.cms.ongoing_project.catalog', compact('catalog'));
    }

    public function catelogUpdate(Request $request){
        // Dosya yükleme limitlerini artır
        ini_set('upload_max_filesize', '100M');
        ini_set('post_max_size', '100M');
        ini_set('max_execution_time', '300');
        ini_set('max_input_time', '300');

        $updated = false;
        $result = 0;
        
        // Public dizinindeki upload/catalog_pdf klasörü
        $uploadPath = public_path('upload/catalog_pdf');
        
        // Klasör yoksa oluştur
        if (!file_exists($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Türkçe katalog
        if($request->hasFile('catalog_tr')){
            $file = $request->file('catalog_tr');
            $extension = $file->getClientOriginalExtension();
            
            if($extension === 'pdf'){
                $fileName = 'katalog_tr_' . date('YmdHis') . '.' . $extension;
                $file->move($uploadPath, $fileName);
                
                $result += OngoingProjectCatalog::where('id', $request->get('catalog_id'))->update([
                    'pdf_tr' => 'upload/catalog_pdf/' . $fileName
                ]);
                
                $updated = true;
            }
        }

        // İngilizce katalog
        if($request->hasFile('catalog_en')){
            $file = $request->file('catalog_en');
            $extension = $file->getClientOriginalExtension();
            
            if($extension === 'pdf'){
                $fileName = 'katalog_en_' . date('YmdHis') . '.' . $extension;
                $file->move($uploadPath, $fileName);
                
                $result += OngoingProjectCatalog::where('id', $request->get('catalog_id'))->update([
                    'pdf_en' => 'upload/catalog_pdf/' . $fileName
                ]);
                
                $updated = true;
            }
        }

        // Almanca katalog
        if($request->hasFile('catalog_de')){
            $file = $request->file('catalog_de');
            $extension = $file->getClientOriginalExtension();
            
            if($extension === 'pdf'){
                $fileName = 'katalog_de_' . date('YmdHis') . '.' . $extension;
                $file->move($uploadPath, $fileName);
                
                $result += OngoingProjectCatalog::where('id', $request->get('catalog_id'))->update([
                    'pdf_de' => 'upload/catalog_pdf/' . $fileName
                ]);
                
                $updated = true;
            }
        }

        if($updated){
            return response()->json("1");
        } else {
            return response()->json("2");
        }
    }

    public function ftpIndex(){

        $ftp = OngoingProjectCatalog::first(['ftp', 'id', 'stock']);

        return view('layouts.cms.ongoing_project.ftp', compact('ftp'));

    }

    public function ftpUpdate(Request $request){

        $rules1 = [

            'ftp' => 'nullable|max:255',
            'stock' => 'nullable|max:255'
        ];

        $data1 = [

            'ftp' => $request->get('ftp'),
            'stock' => $request->get('stock')

        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        $result = OngoingProjectCatalog::where('id', $request->get('id'))->update($data1);

        if ($result > 0) {

            return response()->json([1, 'Ftp Girişi ve Stok Girişi Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }


    }


}