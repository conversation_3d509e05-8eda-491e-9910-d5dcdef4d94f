<?php

namespace App\Http\Controllers\cms;

use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AccountController extends Controller
{
    public function index(){

        return view('layouts.cms.changepassword');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request){

        if(!Hash::check($request->get('old_password'), Auth::user()->password)){

            return response()->json('0');

        }

        if($request->get('new_password') != $request->get('confirm_password')){

            return response()->json("1");

        }

        User::where('id', Auth::user()->id)->update([
            'password' => bcrypt($request->get('new_password'))

        ]);

        return response()->json("2");

    }
}
