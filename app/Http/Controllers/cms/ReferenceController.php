<?php

namespace App\Http\Controllers\cms;

use App\Model\Reference;
use App\Model\ReferenceCategory;
use App\Model\ReferenceGallery;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class ReferenceController extends Controller
{
    public function categoryAddView(){

        return view('layouts.cms.reference.category.view');
    }

    public function createCategory(Request $request){

        $rules4 = [
            'reference_category_tr' => 'required|max:100',
            'reference_category_en' => 'required|max:100',
            'reference_category_de' => 'required|max:100',
        ];

        $data4 = [
            'reference_category_tr' => $request->get('reference_category_tr'),
            'reference_category_en' => $request->get('reference_category_en'),
            'reference_category_de' => $request->get('reference_category_de'),
        ];
        $validator4 = Validator::make($data4, $rules4);
        if ($validator4->fails()) {

            return response()->json([2,$validator4->errors()->toArray()]);
        }

        $result = ReferenceCategory::insert($data4);

        if ($result > 0) {

            return response()->json([1, 'Kategori Başarıyla Eklendi.']);

        } else {

            return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }



    }

    public function categoryTable(){

        $category= ReferenceCategory::where('del', 0)->get();

        return Datatables::of($category)->addColumn('action', function ($category) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="#" id="category_update'.$category->id.'">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? Bu kategoriye ait içerik ve fotoğraflarda silinecektir!\')" 
                                href="' . url("/home/<USER>/" . $category->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';
        })->make(true);
    }

    public function categorySingleGet(Request $request){

        return response()->json(ReferenceCategory::where('id', $request->get('id'))->first());
    }

    public function categoryUpdate(Request $request){

        $rules4 = [
            'reference_category_tr' => 'required|max:100',
            'reference_category_en' => 'required|max:100',
            'reference_category_de' => 'required|max:100',
        ];

        $data4 = [
            'reference_category_tr' => $request->get('reference_category_tr'),
            'reference_category_en' => $request->get('reference_category_en'),
            'reference_category_de' => $request->get('reference_category_de'),
        ];

        $validator4 = Validator::make($data4, $rules4);
        if ($validator4->fails()) {

            return response()->json([2,$validator4->errors()->toArray()]);
        }


        $result = ReferenceCategory::where('id', $request->get('category_id'))->update($data4);

        if ($result > 0) {

            return response()->json([1, 'Kategori Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }

    public function categoryDelete($id){

        ReferenceCategory::where('id',$id)->update([
            'del' => 1
        ]);

        return back();
    }

    public function categoryFrame(){

        $sliderInfo  = ReferenceCategory::where('del', 0)
            ->orderby('row', 'asc')
            ->get();

        return view('layouts.cms.reference.category.frame', compact('sliderInfo'));
    }

    public function categorySort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            ReferenceCategory::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }

    public function index(){

        $categories =ReferenceCategory::where('del', 0)->get();

        return view('layouts.cms.reference.index', compact('categories'));
    }

    public function create(Request $request){

        $rules1 = [
            'reference_category_id'  =>'required',
            'reference_tr'  => 'required|max:255',
            'reference_en'  => 'required|max:255',
            'reference_de'  => 'required|max:255',
            'location'  => 'nullable|max:150',
            'location_en'  => 'nullable|max:150',
            'location_de'  => 'nullable|max:150',
            'employer' => 'nullable|max:150',
            'employer_en' => 'nullable|max:150',
            'employer_de' => 'nullable|max:150',
            'note' => 'nullable|max:150',
            'note_en' => 'nullable|max:150',
            'note_de' => 'nullable|max:150',
        ];

        $data1 = [
            'reference_category_id'  => $request->get('reference_category_id'),
            'reference_tr'  => $request->get('reference_tr'),
            'reference_en'  => $request->get('reference_en'),
            'reference_de'  => $request->get('reference_de'),
            'location'  => $request->get('location'),
            'location_en'  => $request->get('location_en'),
            'location_de'  => $request->get('location_de'),
            'employer' => $request->get('employer'),
            'employer_en' => $request->get('employer_en'),
            'employer_de' => $request->get('employer_de'),
            'note' => $request->get('note'),
            'note_en' => $request->get('note_en'),
            'note_de' => $request->get('note_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');


            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(790, 540)->save(base_path('/upload/reference/' . $fileName));

            $data1['image_url'] = $fileName;

            $result = Reference::insert($data1);

            if ($result > 0) {

                return response()->json([1, 'Referans Başarıyla Eklendi.']);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {
            return response()->json([2, 'Lütfen Resimleri Seçiniz!']);

        }


    }

    public function tableView(){

        return view('layouts.cms.reference.table');
    }

    public function table(){

        $references = Reference::select('reference.id','reference.image_url', 'reference.reference_tr', 'reference.reference_de', 'reference.location',
            'reference.employer', 'reference.note', 'reference_category.reference_category_tr')
            ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
            ->where('reference_category.del', 0)
            ->where('reference.del', 0)
            ->get();

        return Datatables::of($references)->addColumn('action', function ($references) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $references->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $references->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);

    }

    public function portfolyoEditPage($id){

        $categories =ReferenceCategory::where('del',0)->get();
        $reference= Reference::where('id', $id)->first();

        return view('layouts.cms.reference.edit', compact('reference', 'categories'));
    }

    public function update(Request $request){

        $rules1 = [
            'reference_category_id'  =>'required',
            'reference_tr'  => 'required|max:255',
            'reference_en'  => 'required|max:255',
            'reference_de'  => 'required|max:255',
            'location'  => 'nullable|max:150',
            'location_en'  => 'nullable|max:150',
            'location_de'  => 'nullable|max:150',
            'employer' => 'nullable|max:150',
            'employer_en' => 'nullable|max:150',
            'employer_de' => 'nullable|max:150',
            'note' => 'nullable|max:150',
            'note_en' => 'nullable|max:150',
            'note_de' => 'nullable|max:150',
        ];

        $data1 = [
            'reference_category_id'  => $request->get('reference_category_id'),
            'reference_tr'  => $request->get('reference_tr'),
            'reference_en'  => $request->get('reference_en'),
            'reference_de'  => $request->get('reference_de'),
            'location'  => $request->get('location'),
            'location_en'  => $request->get('location_en'),
            'location_de'  => $request->get('location_de'),
            'employer' => $request->get('employer'),
            'employer_en' => $request->get('employer_en'),
            'employer_de' => $request->get('employer_de'),
            'note' => $request->get('note'),
            'note_en' => $request->get('note_en'),
            'note_de' => $request->get('note_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');


            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(790, 540)->save(base_path('/upload/reference/' . $fileName));

            $data1['image_url'] = $fileName;

            $result = Reference::where('id',$request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Referans Başarıyla Güncellendi.', $data1['image_url']]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {

            $result = Reference::where('id',$request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Referans Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }


    }

    public function delete($id){

        Reference::where('id', $id)->update([
            'del' => 1
        ]);

        return back();
    }

    public function orderView(){
        
        $categories = ReferenceCategory::where('del', 0)->get();

        return view('layouts.cms.reference.order', compact('categories'));
    }

    public function frame($id){

        $sliderInfo  = Reference::select(['id', 'image_url', 'reference_tr', 'reference_en', 'reference_de'])->where('del', 0)->where('reference_category_id', $id)->orderby('row', 'asc')->get();

        return view('layouts.cms.reference.order_frame', compact('sliderInfo'));
    }

    public function sort(Request $request){

        $list = explode(',' , $request->get('list_order'));

        $i=1;

        foreach ($list as $value){

            Reference::where('id', $value)->update(['row' => $i]);
            $i++;
        }

        return response()->json("1");

    }

    public function gallery(){

        $reference = Reference::select(['reference.reference_tr', 'reference.id', 'reference_category.reference_category_tr'])
        ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
        ->where('reference.del', 0)
        ->where('reference_category.del', 0)
        ->get();

        return view('layouts.cms.reference.gallery', compact('reference'));
    }

    public function galleryCreate(Request $request){
        
        

        if($request->get('reference_id') ==0){

            return response()->json("2");
        }
        /**
        if(!$request->get('gallery')){

            return response()->json("5");
        }
        **/
        
        $status = 0;

        for ($i = 0; $i < count($request->file('gallery')); $i++) {

            if (isset($request->file('gallery')[$i])) {

                $image = $request->file('gallery')[$i];

                $fileExtension = mb_strtolower($image->getClientOriginalExtension());

                $allowed = array('png', 'jpg', 'jpeg');

                if (!in_array($fileExtension, $allowed)) {

                    return response()->json('0');
                }

                $fileName = sha1(uniqid()) . '.' . $fileExtension;


                Image::make($image)->resize(720, 640)->save(base_path('/upload/reference_gallery/' . $fileName));

                $result = ReferenceGallery::create([

                    'reference_id' => $request->get('reference_id'),
                    'image_url' => $fileName,

                ]);

                $count = count(json_decode($result, true));

                if ($count > 0) {

                    $status++;

                } else {

                    return response()->json("0");
                }


            } else {

                return response()->json('0');
            }


        }
        return response()->json(true);
    }

    public function galleryDeleteView(){

        return view('layouts.cms.reference.portfolyogallerytableview');
    }

    public function galleryEditTable(){

        $reference = Reference::select(['reference_gallery.id', 'reference.reference_tr', 'reference_gallery.image_url'])
            ->join('reference_gallery', 'reference.id', '=', 'reference_gallery.reference_id')
            ->join('reference_category', 'reference_category.id', '=', 'reference.reference_category_id')
            ->where('reference_gallery.del', 0)
            ->where('reference_category.del', 0)
            ->where('reference.del', 0)->get();



        return Datatables::of($reference)->addColumn('action', function ($reference){

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu"> 
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz?\')" href="' . url("/home/<USER>/" . $reference->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';

        })->make(true);
    }

    public function galleryDelete($id){

        ReferenceGallery::where('id', $id)->update([

            'del' => 1
        ]);
        return back();
    }


}