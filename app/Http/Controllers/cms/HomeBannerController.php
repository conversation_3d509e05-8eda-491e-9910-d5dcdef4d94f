<?php

namespace App\Http\Controllers\cms;

use App\Model\HomeBanner;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class HomeBannerController extends Controller
{
    public function index(){

        $banner = HomeBanner::select(['name', 'id'])->get();

        return view('layouts.cms.home_banner.edit', compact('banner'));

    }

    public function bannerGet(Request $request){

        if($request->get('id') == 0){

            return response()->json("500");
        }

        return response()->json(HomeBanner::where('id', $request->get('id') )->first());

    }

    private function messageUpdate($result){

        if ($result > 0) {

            return response()->json([1, 'Anasayfa Banner Başarıyla Güncellendi.']);

        } else {

            return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
        }

    }

    public function update(Request $request){


        if(is_null($request->get('category'))){

            return response()->json([3, '<PERSON>ü<PERSON><PERSON> Kategori Seçiniz!']);
        }

        $rules1 = [
            'title_tr' => 'required|max:255',
            'title_en' => 'required|max:255',
            'title_de' => 'required|max:255',
            'content_tr' => 'nullable',
            'content_en' => 'nullable',
            'content_de' => 'nullable',
        ];

        $data1 = [
            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            $data1['image_url'] = $fileName;

            if($request->get('banner_id') == 1 ){
                Image::make($image)->resize(1920, 1024)->save(base_path('/upload/home_banner/'. $fileName));
            }else{
                Image::make($image)->resize(845, 585)->save(base_path('/upload/home_banner/'. $fileName));
            }



            $result = HomeBanner::where('id', $request->get('banner_id'))->update($data1);

            return $this->messageUpdate($result);


        } else{

            $result = HomeBanner::where('id', $request->get('banner_id'))->update($data1);

            return $this->messageUpdate($result);
        }

    }


}