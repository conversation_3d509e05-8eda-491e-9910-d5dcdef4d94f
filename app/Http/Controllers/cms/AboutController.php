<?php

namespace App\Http\Controllers\cms;

use App\Model\About;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class AboutController extends Controller
{
    public function edit(){

        $about = About::first();

        return view('layouts.cms.corporate.about.index', compact('about'));
    }

    public function update(Request $request){


        $rules1 = [

            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'video' => 'nullable|max:100',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required',
            'title_service_tr' => 'required|max:150',
            'title_service_en' => 'required|max:150',
            'title_service_de' => 'required|max:150',
            'content_service_tr' => 'required',
            'content_service_en' => 'required',
            'content_service_de' => 'required'
        ];

        $data1 = [

            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'video' => $request->get('video'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de'),
            'title_service_tr' => $request->get('title_service_tr'),
            'title_service_en' => $request->get('title_service_en'),
            'title_service_de' => $request->get('title_service_de'),
            'content_service_tr' => $request->get('content_service_tr'),
            'content_service_en' => $request->get('content_service_en'),
            'content_service_de' => $request->get('content_service_de')
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()) {

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image1') && $request->hasFile('image2')) {

            $image1 = $request->file('image1');
            $image2 = $request->file('image2');

            $fileExtension1 = mb_strtolower($image1->getClientOriginalExtension());
            $fileExtension2 = mb_strtolower($image2->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension1, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            if (!in_array($fileExtension2, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName1 = sha1(uniqid()) . '.' . $fileExtension1;
            $fileName2 = sha1(uniqid()) . '.' . $fileExtension2;

            Image::make($image1)->resize(350, 415)->save(base_path('/upload/about/'. $fileName1));
            Image::make($image2)->resize(530, 365)->save(base_path('/upload/about_video/'. $fileName2));

            $data1['image1'] = $fileName1;
            $data1['image2'] = $fileName2;

            $result = About::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hakkımızda Başarıyla Güncellendi.', $data1['image1'], $data1['image2']]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image1')){

            $image = $request->file('image1');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(350, 415)->save(base_path('/upload/about/'. $fileName));

            $data1['image1'] = $fileName;

            $result = About::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hakkımızda Başarıyla Güncellendi.', array("img" => $data1['image1'] )]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else if($request->hasFile('image2')){

            $image = $request->file('image2');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [null, 0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(530, 365)->save(base_path('/upload/about_video/'. $fileName));

            $data1['image2'] = $fileName;

            $result = About::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hakkımızda Başarıyla Güncellendi.', array("video" => $data1['image2'] )]);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }else{


            $result = About::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Hakkımızda Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.', $data1]);
            }

        }


    }
}
