<?php

namespace App\Http\Controllers\cms;

use App\Model\News;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;

class NewsController extends Controller
{
    public function index(){

        return view('layouts.cms.news.index');
    }

    public function create(Request $request){

        if(is_null($request->get('date'))){

            return response()->json([2,'Lütfen Tarih Seçiniz!']);
        }

        $date = explode('-', $request->get('date'));

        $rules1 = [

            'date' => 'date_format:"Y-m-d"|required',
            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'short_content_tr' => 'required|max:255',
            'short_content_en' => 'required|max:255',
            'short_content_de' => 'required|max:255',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required',
        ];

        $data1 = [

            'date' => $date[2].'-'.$date[1].'-'.$date[0],
            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'short_content_tr' => $request->get('short_content_tr'),
            'short_content_en' => $request->get('short_content_en'),
            'short_content_de' => $request->get('short_content_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()){

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(740, 430)->save(base_path('/upload/news/'. $fileName));

            $data1['image_url'] = $fileName;

            $result =   News::insert($data1);

            if ($result > 0) {

                return response()->json([1, 'Haber Başarıyla Eklendi.']);

            } else {

                return response()->json([0, 'Ekleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {

            return response()->json([2,'Lütfen Resim Seçiniz!']);

        }

    }

    public function tableView(){

        return view('layouts.cms.news.table');
    }

    public function table(){

        $news = News::select(['title_tr', 'title_de', 'image_url', 'date', 'short_content_tr', 'id'])->where('del', 0)->get();

        return Datatables::of($news)->addColumn('action', function ($news) {

            return '<div class="btn-group">
                            <button aria-expanded="false" aria-haspopup="true" data-toggle="dropdown"  class="btn btn-danger dropdown-toggle btn-md" type="button">
                                <span>Bir İşlem Seçiniz&nbsp;&nbsp;&nbsp;</span> <span class="caret"></span> <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul class="dropdown-menu">
                                <li><a href="' . url("/home/<USER>/" . $news->id . "") . '">Düzenle</a></li>
                                <li><a onclick="return confirm(\'Silmek istediğinizden emin misiniz? \')" 
                                href="' . url("/home/<USER>/" . $news->id . "") . '">Sil</a></li>
                            </ul>
                        </div>';
        })->make(true);
    }

    public function edit($id){

        $blog = News::where('id', $id)->first();

        return view('layouts.cms.news.edit', compact('blog'));

    }

    public function update(Request $request){

        if(is_null($request->get('date'))){

            return response()->json([2,'Lütfen Tarih Seçiniz!']);
        }

        $date = explode('-', $request->get('date'));

        $rules1 = [

            'date' => 'date_format:"Y-m-d"|required',
            'title_tr' => 'required|max:150',
            'title_en' => 'required|max:150',
            'title_de' => 'required|max:150',
            'short_content_tr' => 'required|max:255',
            'short_content_en' => 'required|max:255',
            'short_content_de' => 'required|max:255',
            'content_tr' => 'required',
            'content_en' => 'required',
            'content_de' => 'required',
        ];

        $data1 = [

            'date' => $date[2].'-'.$date[1].'-'.$date[0],
            'title_tr' => $request->get('title_tr'),
            'title_en' => $request->get('title_en'),
            'title_de' => $request->get('title_de'),
            'short_content_tr' => $request->get('short_content_tr'),
            'short_content_en' => $request->get('short_content_en'),
            'short_content_de' => $request->get('short_content_de'),
            'content_tr' => $request->get('content_tr'),
            'content_en' => $request->get('content_en'),
            'content_de' => $request->get('content_de'),
        ];

        $validator1 = Validator::make($data1, $rules1);

        if ($validator1->fails()){

            return response()->json([2,$validator1->errors()->toArray()]);
        }

        if ($request->hasFile('image')) {

            $image = $request->file('image');

            $fileExtension = mb_strtolower($image->getClientOriginalExtension());

            $allowed = array('png', 'jpg', 'jpeg');

            if (!in_array($fileExtension, $allowed)) {

                return [0, 'Dosya formatı jpg, png, jpeg olabilir!'];
            }

            $fileName = sha1(uniqid()) . '.' . $fileExtension;

            Image::make($image)->resize(740, 430)->save(base_path('/upload/news/'. $fileName));

            $data1['image_url'] = $fileName;

            $result =   News::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Haber Başarıyla Güncellendi.', $data1['image_url']]);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }

        }else {

            $result =   News::where('id', $request->get('id'))->update($data1);

            if ($result > 0) {

                return response()->json([1, 'Haber Başarıyla Güncellendi.']);

            } else {

                return response()->json([0, 'Güncelleme İşlemi Başarısız Oldu!Tekrar Deneyiniz.']);
            }


        }

    }

    public function delete($id){

        News::where('id', $id)->update(['del' => 1]);

        return back();

    }
}
