.p0{
	padding:0 !important;
}

.text-normal{
	font-weight:normal;
}
textarea{
	resize: vertical;
}

.error_m{
	text-align: center;
	color: #a84d4d;
	font-weight: bold;
}

.alert-danger{
	display: inline-block;
	padding: 5px 36px 4px 8px;
}

.alert-danger h4{
	font-size: 14px;
	width: auto !important;
	margin: 0;
	font-weight: normal;
	display: inline-block;
}

.alert-success{
	display: inline-block;
	padding: 5px 36px 4px 8px;
}

.alert-success h4{
	font-size: 14px;
	width: auto !important;
	margin: 0;
	font-weight: normal;
	display: inline-block;
}

.link-button {
	background:none!important;
	border:none;
	padding:0!important;
	font: inherit;
	cursor: pointer;
	width: 100%;
	text-align: left;
	padding: 3px 20px !important;
}

.max-h70{
	max-height: 70px !important;
}
.m-bottom-15{
	margin-bottom: 15px !important;
}

.uploadFile {
	position: absolute;
	z-index: 2;
	top: 0;
	left: 0;
	filter: alpha(opacity=0);
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	opacity: 0;
	background-color: transparent;
	color: transparent;
	height: 31px;
	width: 100px;
	cursor: pointer;
}

a:focus{
	outline: none !important;
}
button:focus{
	outline: none !important;
}

.btn-oniz{
	padding: 4px 12px !important;
	margin-top: 12px;
}

.not-ok{
	color: #d9534f;
}

.ok{
	color: #00a65a;
}
.durum-icon{
	font-size: 18px;
}

.btn-danger.active, .btn-danger:active, .open > .dropdown-toggle.btn-danger {
	background-color: #dd4b39 !important;
	border-color: #d73925 !important;
	color: #fff;
}


.size-checkbox {
	display: none;
}

.size-checkbox + label {
	background-color: #646464;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
	border-radius: 3px;
	display: inline-block;
	position: relative;
    cursor: pointer;
    font-size: 14px;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
	color: #fff;
}

.size-checkbox + label:active, .size-checkbox:checked + label:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.size-checkbox:checked + label {
	background-color: #008D4C;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
	color: #fff;
}

.siparis-detay .table-bordered > thead > tr > th, .siparis-detay .table-bordered > tbody > tr > th, .siparis-detay .table-bordered > tfoot > tr > th, .siparis-detay .table-bordered > thead > tr > td, .siparis-detay .table-bordered > tbody > tr > td, .siparis-detay .table-bordered > tfoot > tr > td {
	border: 1px solid #b2b2b2;
}
@media (max-width:480px) {
	.fiyat-teklif-form-cnt b {
		display: none;
	}


}

.content-wrapper{
	min-height: 800px !important;
}
