.sirala .btn.outlined {
color: #3675B4;
border: solid 2px #3675B4;
border-radius: 3px;
text-transform: uppercase;
background: #fff;
font-size: 18px;
}
.sirala button, html input[type=button], input[type=reset], input[type=submit] {
-webkit-appearance: button;
cursor: pointer;
overflow: visible;
}
.sirala .mleft_no {
margin-left: 0;
}
.sirala .btn {
padding: 10px 20px;
margin: 15px;
border: none;
background: #666;
color: #ccc;
font-weight: bold;
text-decoration: none;
transition: all 0.35s;
-moz-transition: all 0.35s;
-webkit-transition: all 0.35s;
-o-transition: all 0.35s;
white-space: nowrap;
}
.sirala .btn.outlined:hover {
color: #fff;
border: solid 2px #3675B4;
background: #3675B4;
box-shadow: none;
}

.sirala .gallery{ width:100%; float:left; margin-top:100px;}

.sirala .gallery ul{ margin:0; padding:0; list-style-type:none;}

.sirala .gallery ul li{padding: 5px; border:2px solid #ccc; float:left; margin:10px 7px; background:none; width:auto; height:auto;}

.sirala #reorder-helper{margin: 18px 10px;
padding: 10px;}
.light_box {
background: #efefef;
padding: 20px;
margin: 10px 0;
text-align: center;
font-size: 1.2em;
}


.sirala .gallery img{ height:120px;}
